import asyncio
import json
import time
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional, AsyncGenerator
import requests
import websockets
from flask import Flask, request, jsonify, Response
from flask_cors import CORS

# --- 配置信息 ---
# Fairies.ai 代理配置
AGENT_PROTO_ID = "4f18843d-8823-4151-99e2-00fa87eec890"

# 完整的Cookie字符串
FULL_COOKIE_STRING = "gbuuid=6753f271-0ce2-498a-b3e1-30b19a2cb2ed; useKeys=false; osType=web; session=bOqzttrOD8w6cf_y5Z3_UONFJ1ADGWNWTVROC2ZwxZ4; mp_5cfe752f1633fdf743200078293ddde9_mixpanel=%7B%22distinct_id%22%3A%2273d0e7a0-d23d-4132-a37c-cb8f60c75420%22%2C%22%24device_id%22%3A%22d3b994a2-6c58-451c-9b18-981af3896d57%22%2C%22%24initial_referrer%22%3A%22https%3A%2F%2Flinux.do%2F%22%2C%22%24initial_referring_domain%22%3A%22linux.do%22%2C%22__mps%22%3A%7B%7D%2C%22__mpso%22%3A%7B%7D%2C%22__mpus%22%3A%7B%7D%2C%22__mpa%22%3A%7B%7D%2C%22__mpu%22%3A%7B%7D%2C%22__mpr%22%3A%5B%5D%2C%22__mpap%22%3A%5B%5D%2C%22%24user_id%22%3A%2273d0e7a0-d23d-4132-a37c-cb8f60c75420%22%7D; ph_phc_or5z2MyKAus4GbUsu0KmoHbjPYUeoX6xdObszTbBM3a_posthog=%7B%22distinct_id%22%3A%2273d0e7a0-d23d-4132-a37c-cb8f60c75420%22%2C%22%24sesid%22%3A%5B1753842956108%2C%220198592d-dc4a-7bda-8838-40308561c6f7%22%2C1753842834506%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22https%3A%2F%2Flinux.do%2F%22%2C%22u%22%3A%22https%3A%2F%2Ffairies.ai%2F%22%7D%7D"

# API配置
API_HOST = "0.0.0.0"
API_PORT = 5000
API_DEBUG = True

# 支持的API Keys（可以添加多个）
VALID_API_KEYS = [
    "sk-123",
    # "sk-your-custom-key-here",
]

# Fairies.ai API端点
FAIRIES_CREATE_INSTANCE_URL = "https://fairies.ai/api/agent/create_instance"
FAIRIES_WEBSOCKET_URL = "wss://fairies.ai/api/agent/ws"

# 请求头配置
REQUEST_HEADERS = {
    "Content-Type": "application/json",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0"
}

# 模型配置
AVAILABLE_MODELS = [
    {
        "id": "claude-sonnet-4-20250514",
        "object": "model",
        "owned_by": "anthropic"
    }
]

app = Flask(__name__)
CORS(app)

# API 端点
CREATE_INSTANCE_URL = FAIRIES_CREATE_INSTANCE_URL
WEBSOCKET_URL = FAIRIES_WEBSOCKET_URL

headers = {
    **REQUEST_HEADERS,
    "Cookie": FULL_COOKIE_STRING
}

# 验证API Key的函数
def verify_api_key(api_key: str) -> bool:
    """验证API Key是否有效"""
    return api_key in VALID_API_KEYS

def create_agent_instance():
    """创建AI代理实例"""
    payload = {"agent_proto_id": AGENT_PROTO_ID}
    try:
        response = requests.post(CREATE_INSTANCE_URL, headers=headers, json=payload)
        response.raise_for_status()
        data = response.json()
        agent_id = data.get("id")
        return agent_id
    except requests.exceptions.RequestException as e:
        print(f"创建代理实例失败: {e}")
        return None

async def start_chat(agent_id, query, stream=False):
    """直接从demo.py复制的WebSocket对话函数"""
    try:
        # 完全按照demo.py的方式构建headers
        demo_headers = {
            "Content-Type": "application/json",
            "Cookie": FULL_COOKIE_STRING,
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0"
        }
        async with websockets.connect(WEBSOCKET_URL, extra_headers=demo_headers) as websocket:
            user_message = { "type": "USER_MESSAGE", "agent_id": agent_id, "observation": { "message": { "user_query": query, "mode": "main", "files": [], "memories": [], "model": "claude-sonnet-4-20250514", "task_id": None, "workspace_path": "/" } } }
            await websocket.send(json.dumps(user_message))

            full_content = ""
            is_assistant_turn = False # 增加一个"开关"，判断是否轮到AI发言

            async for message in websocket:
                try:
                    inner_json_string = json.loads(message)
                    parsed_message = json.loads(inner_json_string)
                except (json.JSONDecodeError, TypeError):
                    parsed_message = json.loads(message)

                msg_type = parsed_message.get("type")
                if msg_type == "CHAT_MESSAGE":
                    inner_msg = parsed_message.get("message", {}).get("message", {})
                    inner_type = inner_msg.get("type")
                    role = inner_msg.get("item", {}).get("role")

                    # 当检测到是 assistant 的回合开始时，打开"开关"
                    if inner_type == "response.output_item.added" and role == "assistant":
                        is_assistant_turn = True

                    # 只有当"开关"打开时，才处理文本
                    if is_assistant_turn and inner_type == "response.output_text.delta":
                        delta_text = inner_msg.get("delta", "")
                        full_content += delta_text

                        if stream:
                            yield delta_text

                    # 当 assistant 的回合结束时，关闭"开关"并退出
                    elif inner_type == "response.output_item.done" and role == "assistant":
                        is_assistant_turn = False
                        break

            if not stream:
                yield full_content

    except Exception as e:
        raise Exception(f"WebSocket 通信出错: {e}")

async def chat_with_agent(agent_id: str, messages: List[Dict[str, str]], stream: bool = False):
    """与AI代理进行对话的包装函数"""
    # 提取最后一条用户消息
    user_query = ""
    for message in reversed(messages):
        if message.get("role") == "user":
            user_query = message.get("content", "")
            break

    if not user_query:
        raise ValueError("没有找到用户消息")

    # 直接使用demo.py中的逻辑，但不导入demo模块
    try:
        # 完全复制demo.py的start_chat函数逻辑
        demo_headers = {
            "Content-Type": "application/json",
            "Cookie": FULL_COOKIE_STRING,
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0"
        }

        async with websockets.connect(WEBSOCKET_URL, additional_headers=demo_headers) as websocket:
            user_message = { "type": "USER_MESSAGE", "agent_id": agent_id, "observation": { "message": { "user_query": user_query, "mode": "main", "files": [], "memories": [], "model": "claude-sonnet-4-20250514", "task_id": None, "workspace_path": "/" } } }
            await websocket.send(json.dumps(user_message))

            full_content = ""
            is_assistant_turn = False # 增加一个"开关"，判断是否轮到AI发言

            async for message in websocket:
                try:
                    inner_json_string = json.loads(message)
                    parsed_message = json.loads(inner_json_string)
                except (json.JSONDecodeError, TypeError):
                    parsed_message = json.loads(message)

                msg_type = parsed_message.get("type")
                if msg_type == "CHAT_MESSAGE":
                    inner_msg = parsed_message.get("message", {}).get("message", {})
                    inner_type = inner_msg.get("type")
                    role = inner_msg.get("item", {}).get("role")

                    # 当检测到是 assistant 的回合开始时，打开"开关"
                    if inner_type == "response.output_item.added" and role == "assistant":
                        is_assistant_turn = True

                    # 只有当"开关"打开时，才处理文本
                    if is_assistant_turn and inner_type == "response.output_text.delta":
                        delta_text = inner_msg.get("delta", "")
                        full_content += delta_text

                        if stream:
                            yield delta_text

                    # 当 assistant 的回合结束时，关闭"开关"并退出
                    elif inner_type == "response.output_item.done" and role == "assistant":
                        is_assistant_turn = False
                        break

            if not stream:
                yield full_content

    except Exception as e:
        raise Exception(f"WebSocket 通信出错: {e}")

@app.route('/v1/chat/completions', methods=['POST'])
def chat_completions():
    """OpenAI兼容的聊天完成API"""
    # 验证API Key
    auth_header = request.headers.get('Authorization', '')
    if not auth_header.startswith('Bearer '):
        return jsonify({"error": {"message": "Invalid authorization header", "type": "invalid_request_error"}}), 401
    
    api_key = auth_header[7:]  # 移除 "Bearer " 前缀
    if not verify_api_key(api_key):
        return jsonify({"error": {"message": "Invalid API key", "type": "invalid_request_error"}}), 401
    
    # 解析请求数据
    data = request.get_json()
    if not data:
        return jsonify({"error": {"message": "Invalid JSON", "type": "invalid_request_error"}}), 400
    
    messages = data.get('messages', [])
    model = data.get('model', 'gpt-3.5-turbo')
    stream = data.get('stream', False)
    temperature = data.get('temperature', 1.0)
    max_tokens = data.get('max_tokens')
    
    if not messages:
        return jsonify({"error": {"message": "Messages are required", "type": "invalid_request_error"}}), 400
    
    # 创建代理实例
    agent_id = create_agent_instance()
    if not agent_id:
        return jsonify({"error": {"message": "Failed to create agent instance", "type": "internal_server_error"}}), 500
    
    completion_id = f"chatcmpl-{uuid.uuid4().hex[:29]}"
    created_timestamp = int(time.time())
    
    if stream:
        def generate_stream():
            try:
                # 使用asyncio.run来运行异步代码
                async def run_async_stream():
                    async def stream_generator():
                        async for content in chat_with_agent(agent_id, messages, stream=True):
                            chunk = {
                                "id": completion_id,
                                "object": "chat.completion.chunk",
                                "created": created_timestamp,
                                "model": model,
                                "choices": [{
                                    "index": 0,
                                    "delta": {"content": content},
                                    "finish_reason": None
                                }]
                            }
                            yield f"data: {json.dumps(chunk)}\n\n"

                        # 发送结束标记
                        final_chunk = {
                            "id": completion_id,
                            "object": "chat.completion.chunk",
                            "created": created_timestamp,
                            "model": model,
                            "choices": [{
                                "index": 0,
                                "delta": {},
                                "finish_reason": "stop"
                            }]
                        }
                        yield f"data: {json.dumps(final_chunk)}\n\n"
                        yield "data: [DONE]\n\n"

                    # 收集所有流式数据
                    chunks = []
                    async for chunk in stream_generator():
                        chunks.append(chunk)
                    return chunks

                # 运行异步函数并获取结果
                chunks = asyncio.run(run_async_stream())
                for chunk in chunks:
                    yield chunk

            except Exception as e:
                error_chunk = {
                    "error": {
                        "message": str(e),
                        "type": "internal_server_error"
                    }
                }
                yield f"data: {json.dumps(error_chunk)}\n\n"

        return Response(generate_stream(), mimetype='text/event-stream', headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Content-Type': 'text/event-stream; charset=utf-8'
        })
    
    else:
        # 非流式响应
        try:
            async def get_response():
                async for content in chat_with_agent(agent_id, messages, stream=False):
                    return content

            full_response = asyncio.run(get_response())

            response = {
                "id": completion_id,
                "object": "chat.completion",
                "created": created_timestamp,
                "model": model,
                "choices": [{
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": full_response
                    },
                    "finish_reason": "stop"
                }],
                "usage": {
                    "prompt_tokens": 0,  # 这里可以根据实际情况计算
                    "completion_tokens": 0,
                    "total_tokens": 0
                }
            }

            return jsonify(response)

        except Exception as e:
            return jsonify({"error": {"message": str(e), "type": "internal_server_error"}}), 500

@app.route('/v1/models', methods=['GET'])
def list_models():
    """列出可用的模型"""
    models_data = []
    for model in AVAILABLE_MODELS:
        model_info = {
            "id": model["id"],
            "object": "model",
            "created": int(time.time()),
            "owned_by": model.get("owned_by", "openai")
        }
        models_data.append(model_info)

    models = {
        "object": "list",
        "data": models_data
    }
    return jsonify(models)

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    return jsonify({"status": "healthy", "timestamp": datetime.now().isoformat()})

if __name__ == '__main__':
    print("OpenAI兼容API服务器启动中...")
    print(f"API端点: http://{API_HOST}:{API_PORT}/v1/chat/completions")
    print(f"API Keys: {', '.join(VALID_API_KEYS)}")
    print(f"模型列表: http://{API_HOST}:{API_PORT}/v1/models")
    print(f"健康检查: http://{API_HOST}:{API_PORT}/health")
    print(f"代理ID: {AGENT_PROTO_ID[:8]}...{AGENT_PROTO_ID[-8:] if len(AGENT_PROTO_ID) > 16 else AGENT_PROTO_ID}")
    print(f"Cookie状态: {'已配置' if 'session=' in FULL_COOKIE_STRING else '未配置'}")
    app.run(host=API_HOST, port=API_PORT, debug=API_DEBUG)

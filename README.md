---
title: OpenAI Compatible API
emoji: 🤖
colorFrom: blue
colorTo: green
sdk: docker
pinned: false
license: mit
---

# OpenAI兼容API服务器

将Fairies.ai代理包装为OpenAI兼容的API，可以与任何支持OpenAI API的客户端库或应用程序一起使用。

## 🚀 功能特性

- ✅ 完全兼容OpenAI Chat Completions API
- ✅ 支持流式和非流式响应
- ✅ API Key认证
- ✅ 标准的错误处理
- ✅ 健康检查端点
- ✅ 模型列表端点

## 🔧 环境变量配置

在Hugging Face Space的设置中配置以下环境变量：

| 变量名 | 描述 | 示例 | 必需 |
|--------|------|------|------|
| `AGENT_PROTO_ID` | Fairies.ai代理ID | `4f18843d-8823-4151-99e2-00fa87eec890` | ✅ |
| `FULL_COOKIE_STRING` | 完整的Cookie字符串 | `gbuuid=xxx; session=xxx; ...` | ✅ |
| `VALID_API_KEYS` | 有效的API Keys | `["sk-123","sk-456"]` 或 `sk-123,sk-456` | ✅ |
| `API_DEBUG` | 调试模式 | `false` | ❌ |

## 📡 API使用

### 基础URL
```
https://your-space-name.hf.space
```

### 聊天完成

```bash
curl -X POST https://your-space-name.hf.space/v1/chat/completions \
  -H "Authorization: Bearer sk-123" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-sonnet-4-20250514",
    "messages": [
      {"role": "user", "content": "你好！"}
    ],
    "stream": false
  }'
```

### 使用OpenAI Python库

```python
from openai import OpenAI

client = OpenAI(
    api_key="sk-123",
    base_url="https://your-space-name.hf.space/v1"
)

response = client.chat.completions.create(
    model="claude-sonnet-4-20250514",
    messages=[
        {"role": "user", "content": "你好！"}
    ]
)

print(response.choices[0].message.content)
```

### 流式响应

```python
stream = client.chat.completions.create(
    model="claude-sonnet-4-20250514",
    messages=[
        {"role": "user", "content": "写一首诗"}
    ],
    stream=True
)

for chunk in stream:
    if chunk.choices[0].delta.content is not None:
        print(chunk.choices[0].delta.content, end="")
```

## 🔧 API端点

- `GET /health` - 健康检查
- `GET /v1/models` - 获取可用模型列表
- `POST /v1/chat/completions` - 聊天完成（支持流式和非流式）

## 🔒 获取配置信息

### 获取AGENT_PROTO_ID和FULL_COOKIE_STRING

1. 打开浏览器开发者工具
2. 访问 [fairies.ai](https://fairies.ai)
3. 在网络标签页中找到相关请求
4. 复制Cookie字符串和代理ID

## 🚀 部署到Hugging Face Space

1. **创建新的Space**：
   - 访问 [Hugging Face Spaces](https://huggingface.co/spaces)
   - 点击 "Create new Space"
   - 选择 "Docker" 作为SDK

2. **上传文件**：
   - `Dockerfile`
   - `openai_compatible_api.py`
   - `requirements.txt`
   - `README.md`

3. **配置环境变量**：
   - 在Space设置中添加环境变量
   - `AGENT_PROTO_ID`: 你的代理ID
   - `FULL_COOKIE_STRING`: 你的Cookie字符串
   - `VALID_API_KEYS`: 你的API Keys

4. **部署**：
   - Space会自动构建Docker镜像并部署

## 📝 注意事项

- 确保Cookie字符串是最新的，过期后需要重新获取
- API Keys可以自定义，支持多个（JSON数组或逗号分隔）
- 在生产环境中建议使用更安全的API Key
- Space的URL格式为：`https://your-username-space-name.hf.space`

## 🐛 故障排除

1. **部署失败**：检查Dockerfile语法和依赖
2. **API调用失败**：检查环境变量是否正确设置
3. **认证错误**：检查Cookie是否过期，API Key是否正确

## 📄 许可证

MIT License
